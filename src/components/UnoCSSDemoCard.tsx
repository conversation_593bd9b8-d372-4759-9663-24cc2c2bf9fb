import React from 'react';

/**
 * UnoCSS 功能演示组件
 * 展示 UnoCSS 的各种高级功能
 */
const UnoCSSDemoCard: React.FC = () => {
  return (
    <div className="p-6 space-y-4">
      {/* 基础样式 - 与 Tailwind 兼容 */}
      <div className="rounded-lg bg-blue-500 p-4 text-white">
        基础样式（与 Tailwind 兼容）
      </div>

      {/* 自定义快捷方式 */}
      <div className="card-base p-4">自定义快捷方式：card-base</div>

      {/* 任意值语法 - 更灵活 */}
      <div className="rounded-[8px] bg-[#ff6b6b] p-[15px] text-[20px]">
        任意值语法：自定义颜色和尺寸
      </div>

      {/* 属性化模式 - UnoCSS 独有 */}
      <div
        bg="gradient-to-r from-purple-400 to-pink-400"
        text="white center"
        p="4"
        rounded="lg"
        shadow="lg"
      >
        属性化模式（Attributify）
      </div>

      {/* 响应式设计 */}
      <div className="w-full rounded bg-green-500 p-4 text-white lg:w-1/4 md:w-1/3 sm:w-1/2">
        响应式宽度
      </div>

      {/* 自定义规则示例 */}
      <div className="m-20px border border-yellow-300 rounded bg-yellow-100 p-15px">
        自定义规则：m-20px p-15px
      </div>

      {/* 变体组语法 */}
      <button className="transform rounded bg-gray-200 px-4 py-2 text-white transition-all hover:(scale-105 bg-blue-500)">
        变体组语法：悬停效果
      </button>

      {/* CSS 网格示例 */}
      <div className="grid grid-cols-[200px_1fr_100px] mt-4 gap-4">
        <div className="bg-red-100 p-2 text-center">200px</div>
        <div className="bg-blue-100 p-2 text-center">弹性</div>
        <div className="bg-green-100 p-2 text-center">100px</div>
      </div>
    </div>
  );
};

export default UnoCSSDemoCard;
