import React, { useState, useMemo } from 'react';
import { type Permission } from '@/router/routeMap';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store';
import { message } from 'antd';
import { useLanguage } from '../hooks/useLanguage';

interface RequireAuthProps {
  children: React.ReactNode;
  /**
   * 是否检查当前路由是否在用户权限中
   * 默认为 true，如果设置为 false 则只检查登录状态
   */
  checkPermission?: boolean;
}

/**
 * 路由守卫组件
 * 用于保护需要认证的路由
 * 如果用户未登录，会重定向到登录页面
 * 如果用户已登录但没有访问权限，会返回 null（不渲染内容）
 */
const RequireAuth: React.FC<RequireAuthProps> = ({
  children,
  checkPermission = true,
}) => {
  const location = useLocation();
  const { isAuthenticated, permissions } = useAuthStore();
  const { t } = useLanguage();
  const [messageShown, setMessageShown] = useState(false);

  const permissionUrls = useMemo(() => {
    const urls: string[] = [];
    const traverse = (perms: typeof permissions) => {
      perms.forEach(perm => {
        if (perm.url && !perm.url.includes('button:')) {
          urls.push(perm.url);
        }
        if (perm.children?.length) {
          traverse(perm.children);
        }
      });
    };
    traverse(permissions);
    return urls;
  }, [permissions]);

  // 初始加载时检查 - 如果未登录，立即重定向
  if (!isAuthenticated) {
    if (!messageShown) {
      // message.warning(t('auth.permission.login'));
      setMessageShown(true);
    }

    // 重定向到登录页面，并保存当前路径以便登录后返回
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  console.log('location---', location);
  if (checkPermission && location.pathname === '/login') {
    console.log('登录页面跳转----');

    if (permissionUrls.includes(location.state?.from?.pathname)) {
      console.log('有权限直接跳转');

      return <>{children}</>;
    } else {
      console.log('无权限跳转----/');

      return <Navigate to="/" replace state={{ from: location }} />;
    }
  }
  // 如果需要检查权限且当前路径不在权限列表中，返回null
  if (checkPermission && !permissionUrls.includes(location.pathname)) {
    console.warn(`用户尝试访问无权限的路径: ${location.pathname}`);
    return null;
  }
  console.log('有权限', children);

  // 用户已登录且有权限访问，渲染子组件
  return <>{children}</>;
};

export default RequireAuth;
