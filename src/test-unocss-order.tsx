import React from 'react';

// 测试UnoCSS类名排序
export const TestComponent = () => {
  return (
    <div>
      {/* 故意打乱的类名顺序，应该被ESLint自动修复 */}
      <div className="m-2 border rounded bg-blue-100 p-4 text-red-500">
        测试类名排序
      </div>
      
      {/* 更复杂的类名组合 */}
      <div className="border border-gray-300 rounded-md bg-white px-4 py-2 text-sm text-gray-700 font-medium shadow-sm transition-colors duration-200 active:bg-gray-300 hover:bg-gray-200 focus:outline-none">
        复杂类名测试
      </div>
      
      {/* 使用clsx函数的情况 */}
      <div className={clsx(
        'm-1 border-2 rounded-lg bg-yellow-100 p-3 text-blue-600',
        'hover:bg-yellow-200 focus:ring-2 focus:ring-blue-500'
      )}>
        clsx函数测试
      </div>
    </div>
  );
};

// 模拟clsx函数
function clsx(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}
